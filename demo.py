"""
Demo script for RLE Bottom-up Multi-Person Pose Estimation and Tracking

This script can process:
1. Single images
2. Image sequences (directories)
3. Videos

Usage:
    python demo.py --input path/to/input --checkpoint path/to/model.pth [options]
"""

import os
import sys
import argparse
import cv2
import numpy as np
import torch
from PIL import Image
import glob
from tqdm import tqdm

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.default import get_config
from src.models import RLEBottomUpModel
from src.data.transforms import get_transforms
from src.inference import PostProcessor, RLETracker
from src.utils.checkpoint import load_model_weights
from src.utils.visualization import draw_pose, create_video_from_frames


def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='RLE Bottom-up Pose Estimation and Tracking Demo')
    
    parser.add_argument('--input', type=str, required=True,
                        help='Input path (image, directory, or video)')
    parser.add_argument('--checkpoint', type=str, required=True,
                        help='Path to model checkpoint')
    parser.add_argument('--output', type=str, default='./demo_output',
                        help='Output directory')
    parser.add_argument('--device', type=str, default='cuda',
                        choices=['cuda', 'cpu'], help='Device to use')
    parser.add_argument('--save_video', action='store_true',
                        help='Save output as video (for image sequences)')
    parser.add_argument('--fps', type=int, default=30,
                        help='Output video FPS')
    parser.add_argument('--conf_threshold', type=float, default=0.3,
                        help='Confidence threshold for pose detection')
    
    return parser.parse_args()


class PoseEstimationDemo:
    """Demo class for pose estimation and tracking"""
    
    def __init__(self, checkpoint_path, device='cuda'):
        """
        Initialize demo
        
        Args:
            checkpoint_path: Path to model checkpoint
            device: Device to use ('cuda' or 'cpu')
        """
        self.device = torch.device(device)
        
        # Load configuration
        self.cfg = get_config()
        
        # Create model
        self.model = RLEBottomUpModel(self.cfg.MODEL)
        self.model = load_model_weights(self.model, checkpoint_path)
        self.model = self.model.to(self.device)
        self.model.eval()
        
        # Create transforms
        self.transforms = get_transforms(self.cfg, is_train=False)
        
        # Create post-processor and tracker
        self.post_processor = PostProcessor(self.cfg)
        self.tracker = RLETracker(self.cfg)
        
        print(f"Model loaded from: {checkpoint_path}")
        print(f"Using device: {self.device}")
    
    def preprocess_image(self, image):
        """
        Preprocess image for model input
        
        Args:
            image: PIL Image or numpy array
            
        Returns:
            Preprocessed tensor
        """
        if isinstance(image, np.ndarray):
            image = Image.fromarray(image)
        
        # Apply transforms
        image_tensor, _ = self.transforms(image, None)
        
        # Add batch dimension
        image_tensor = image_tensor.unsqueeze(0)
        
        return image_tensor
    
    def predict_single_image(self, image, use_tracking=False):
        """
        Predict poses for a single image
        
        Args:
            image: Input image (PIL Image or numpy array)
            use_tracking: Whether to use tracking
            
        Returns:
            List of pose instances with track IDs
        """
        # Preprocess image
        image_tensor = self.preprocess_image(image)
        image_tensor = image_tensor.to(self.device)
        
        # Forward pass
        with torch.no_grad():
            predictions = self.model(image_tensor)
        
        # Post-process predictions
        pose_instances = self.post_processor.process(predictions)
        
        # Apply tracking if enabled
        if use_tracking:
            pose_instances = self.tracker.update(pose_instances[0], predictions)
        else:
            pose_instances = pose_instances[0]
        
        return pose_instances
    
    def process_image(self, image_path, output_path, use_tracking=False):
        """
        Process a single image
        
        Args:
            image_path: Path to input image
            output_path: Path to save output image
            use_tracking: Whether to use tracking
        """
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            print(f"Error: Could not load image {image_path}")
            return
        
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Predict poses
        pose_instances = self.predict_single_image(image_rgb, use_tracking)
        
        # Draw poses on image
        output_image = image.copy()
        for pose in pose_instances:
            track_id = pose.get('track_id', None)
            output_image = draw_pose(
                output_image, 
                pose['keypoints'], 
                pose['visibility'],
                track_id=track_id
            )
        
        # Save output
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        cv2.imwrite(output_path, output_image)
        print(f"Output saved to: {output_path}")
    
    def process_image_sequence(self, input_dir, output_dir, save_video=False, fps=30):
        """
        Process a sequence of images
        
        Args:
            input_dir: Directory containing input images
            output_dir: Directory to save output images
            save_video: Whether to create output video
            fps: Video FPS
        """
        # Get image files
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp']
        image_files = []
        for ext in image_extensions:
            image_files.extend(glob.glob(os.path.join(input_dir, ext)))
            image_files.extend(glob.glob(os.path.join(input_dir, ext.upper())))
        
        image_files.sort()
        
        if len(image_files) == 0:
            print(f"No images found in {input_dir}")
            return
        
        print(f"Processing {len(image_files)} images...")
        
        # Reset tracker for new sequence
        self.tracker.reset()
        
        # Process images
        output_paths = []
        for i, image_path in enumerate(tqdm(image_files)):
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                continue
            
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Predict poses with tracking
            pose_instances = self.predict_single_image(image_rgb, use_tracking=True)
            
            # Draw poses
            output_image = image.copy()
            for pose in pose_instances:
                track_id = pose.get('track_id', None)
                output_image = draw_pose(
                    output_image,
                    pose['keypoints'],
                    pose['visibility'],
                    track_id=track_id
                )
            
            # Save output image
            output_filename = f"frame_{i:06d}.jpg"
            output_path = os.path.join(output_dir, output_filename)
            os.makedirs(output_dir, exist_ok=True)
            cv2.imwrite(output_path, output_image)
            output_paths.append(output_path)
        
        print(f"Processed images saved to: {output_dir}")
        
        # Create video if requested
        if save_video and len(output_paths) > 0:
            video_path = os.path.join(output_dir, 'output_video.mp4')
            create_video_from_frames(output_paths, video_path, fps)
    
    def process_video(self, video_path, output_dir, save_video=False, fps=30):
        """
        Process a video file
        
        Args:
            video_path: Path to input video
            output_dir: Directory to save output
            save_video: Whether to create output video
            fps: Output video FPS
        """
        # Open video
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"Error: Could not open video {video_path}")
            return
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        print(f"Processing video with {total_frames} frames...")
        
        # Reset tracker
        self.tracker.reset()
        
        # Process frames
        output_paths = []
        frame_idx = 0
        
        with tqdm(total=total_frames) as pbar:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # Predict poses with tracking
                pose_instances = self.predict_single_image(frame_rgb, use_tracking=True)
                
                # Draw poses
                output_frame = frame.copy()
                for pose in pose_instances:
                    track_id = pose.get('track_id', None)
                    output_frame = draw_pose(
                        output_frame,
                        pose['keypoints'],
                        pose['visibility'],
                        track_id=track_id
                    )
                
                # Save frame
                output_filename = f"frame_{frame_idx:06d}.jpg"
                output_path = os.path.join(output_dir, output_filename)
                os.makedirs(output_dir, exist_ok=True)
                cv2.imwrite(output_path, output_frame)
                output_paths.append(output_path)
                
                frame_idx += 1
                pbar.update(1)
        
        cap.release()
        print(f"Processed frames saved to: {output_dir}")
        
        # Create output video if requested
        if save_video and len(output_paths) > 0:
            video_output_path = os.path.join(output_dir, 'output_video.mp4')
            create_video_from_frames(output_paths, video_output_path, fps)


def main():
    """Main function"""
    args = parse_args()
    
    # Check if checkpoint exists
    if not os.path.exists(args.checkpoint):
        print(f"Error: Checkpoint not found: {args.checkpoint}")
        return
    
    # Check if input exists
    if not os.path.exists(args.input):
        print(f"Error: Input not found: {args.input}")
        return
    
    # Create demo instance
    demo = PoseEstimationDemo(args.checkpoint, args.device)
    
    # Determine input type and process accordingly
    if os.path.isfile(args.input):
        if args.input.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
            # Video file
            print("Processing video...")
            demo.process_video(args.input, args.output, args.save_video, args.fps)
        else:
            # Single image
            print("Processing single image...")
            output_path = os.path.join(args.output, 'output.jpg')
            demo.process_image(args.input, output_path)
    elif os.path.isdir(args.input):
        # Image sequence
        print("Processing image sequence...")
        demo.process_image_sequence(args.input, args.output, args.save_video, args.fps)
    else:
        print(f"Error: Invalid input type: {args.input}")


if __name__ == '__main__':
    main()
