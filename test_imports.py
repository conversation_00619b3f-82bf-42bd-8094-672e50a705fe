"""
Test script to verify all modules can be imported correctly
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test all module imports"""
    
    print("Testing imports...")
    
    try:
        # Test config
        from config.default import get_config
        print("✓ Config module imported successfully")
        
        # Test models
        from src.models import RLEBottomUpModel, ResNetBackbone, RealNVP
        print("✓ Model modules imported successfully")
        
        # Test data
        from src.data import PoseTrackDataset, get_transforms
        print("✓ Data modules imported successfully")
        
        # Test inference
        from src.inference import PostProcessor, RLETracker
        print("✓ Inference modules imported successfully")
        
        # Test utils
        from src.utils.logger import setup_logger
        from src.utils.checkpoint import save_checkpoint, load_checkpoint
        from src.utils.metrics import compute_metrics
        from src.utils.visualization import visualize_predictions
        print("✓ Utility modules imported successfully")
        
        print("\n✅ All imports successful!")
        return True
        
    except ImportError as e:
        print(f"\n❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False


def test_model_creation():
    """Test model creation with default config"""
    
    print("\nTesting model creation...")
    
    try:
        from config.default import get_config
        from src.models import RLEBottomUpModel
        
        # Get default config
        cfg = get_config()
        
        # Create model
        model = RLEBottomUpModel(cfg.MODEL)
        
        # Count parameters
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"✓ Model created successfully")
        print(f"  - Total parameters: {total_params:,}")
        print(f"  - Trainable parameters: {trainable_params:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model creation error: {e}")
        return False


def test_config():
    """Test configuration loading"""
    
    print("\nTesting configuration...")
    
    try:
        from config.default import get_config
        
        cfg = get_config()
        
        print(f"✓ Configuration loaded successfully")
        print(f"  - Number of joints: {cfg.MODEL.NUM_JOINTS}")
        print(f"  - Image size: {cfg.MODEL.IMAGE_SIZE}")
        print(f"  - Backbone layers: {cfg.MODEL.BACKBONE_LAYERS}")
        print(f"  - Batch size: {cfg.TRAIN.BATCH_SIZE}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False


if __name__ == '__main__':
    print("PKTrack - Module Import Test")
    print("=" * 40)
    
    success = True
    
    # Test imports
    success &= test_imports()
    
    # Test configuration
    success &= test_config()
    
    # Test model creation
    success &= test_model_creation()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 All tests passed! The project is ready to use.")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
    
    sys.exit(0 if success else 1)
