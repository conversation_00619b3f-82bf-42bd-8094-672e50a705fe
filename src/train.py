"""
Training script for RLE Bottom-up Multi-Person Pose Estimation and Tracking
"""

import os
import sys
import argparse
import time
from datetime import datetime

import torch
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.amp import GradScaler, autocast

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.default import get_config, update_config
from src.models import RLEBottomUpModel
from src.data import PoseTrackDataset
from src.utils.logger import setup_logger
from src.utils.checkpoint import save_checkpoint, load_checkpoint
from src.utils.visualization import visualize_predictions


def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Train RLE Bottom-up Pose Estimation and Tracking')
    
    # Data arguments
    parser.add_argument('--data_root', type=str, default='./datasets/PoseTrack21/data',
                        help='Root directory of the dataset')
    parser.add_argument('--output_dir', type=str, default='./outputs',
                        help='Output directory for checkpoints and logs')
    
    # Training arguments
    parser.add_argument('--batch_size', type=int, default=16,
                        help='Batch size for training')
    parser.add_argument('--num_workers', type=int, default=8,
                        help='Number of data loading workers')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of training epochs')
    parser.add_argument('--lr', type=float, default=1e-4,
                        help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-4,
                        help='Weight decay')
    parser.add_argument('--lr_schedule', type=str, default='cosine',
                        choices=['cosine', 'step', 'plateau'],
                        help='Learning rate schedule')
    parser.add_argument('--warmup_epochs', type=int, default=5,
                        help='Number of warmup epochs')
    
    # Loss weights
    parser.add_argument('--keypoint_weight', type=float, default=1.0,
                        help='Weight for keypoint loss')
    parser.add_argument('--spatial_weight', type=float, default=1.0,
                        help='Weight for spatial connection loss')
    parser.add_argument('--temporal_weight', type=float, default=1.0,
                        help='Weight for temporal connection loss')
    
    # Checkpoint and evaluation
    parser.add_argument('--save_freq', type=int, default=10,
                        help='Save checkpoint every N epochs')
    parser.add_argument('--eval_freq', type=int, default=5,
                        help='Evaluate every N epochs')
    parser.add_argument('--resume', type=str, default='',
                        help='Path to checkpoint to resume from')
    
    # Other options
    parser.add_argument('--mixed_precision', action='store_true',
                        help='Use mixed precision training')
    parser.add_argument('--backbone_pretrained', action='store_true',
                        help='Use pretrained backbone')
    parser.add_argument('--debug', action='store_true',
                        help='Debug mode with smaller dataset')
    
    return parser.parse_args()


def create_data_loaders(cfg):
    """Create training and validation data loaders"""
    
    # Training dataset
    train_dataset = PoseTrackDataset(cfg, split='train')
    train_loader = DataLoader(
        train_dataset,
        batch_size=cfg.TRAIN.BATCH_SIZE,
        shuffle=True,
        num_workers=cfg.TRAIN.NUM_WORKERS,
        pin_memory=True,
        drop_last=True
    )
    
    # Validation dataset
    val_dataset = PoseTrackDataset(cfg, split='val')
    val_loader = DataLoader(
        val_dataset,
        batch_size=cfg.VAL.BATCH_SIZE,
        shuffle=False,
        num_workers=cfg.TRAIN.NUM_WORKERS,
        pin_memory=True,
        drop_last=False
    )
    
    return train_loader, val_loader, train_dataset, val_dataset


def create_model_and_optimizer(cfg):
    """Create model, optimizer, and scheduler"""
    
    # Create model
    model = RLEBottomUpModel(cfg.MODEL)
    
    # Move to device
    device = torch.device(cfg.DEVICE)
    model = model.to(device)
    
    # Create optimizer
    optimizer = optim.AdamW(
        model.parameters(),
        lr=cfg.TRAIN.LR,
        weight_decay=cfg.TRAIN.WEIGHT_DECAY
    )
    
    # Create scheduler
    if cfg.TRAIN.LR_SCHEDULE == 'cosine':
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=cfg.TRAIN.EPOCHS
        )
    elif cfg.TRAIN.LR_SCHEDULE == 'step':
        scheduler = optim.lr_scheduler.StepLR(
            optimizer, step_size=30, gamma=0.1
        )
    elif cfg.TRAIN.LR_SCHEDULE == 'plateau':
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', patience=10
        )
    else:
        scheduler = None
    
    return model, optimizer, scheduler


def train_epoch(model, train_loader, optimizer, scaler, cfg, epoch, logger, global_step=0):
    """Train for one epoch"""
    model.train()

    total_loss = 0.0
    total_keypoint_loss = 0.0
    total_spatial_loss = 0.0
    total_temporal_loss = 0.0

    device = torch.device(cfg.DEVICE)
    
    for batch_idx, (images, labels) in enumerate(train_loader):
        # Move data to device
        images = images.to(device)
        for key in labels:
            if isinstance(labels[key], torch.Tensor):
                labels[key] = labels[key].to(device)
        
        optimizer.zero_grad()
        
        # Forward pass with mixed precision
        if cfg.MIXED_PRECISION:
            with autocast('cuda'):
                outputs = model(images, labels)

                # Compute weighted loss
                loss = 0.0
                if 'keypoint_loss' in outputs:
                    loss += cfg.TRAIN.KEYPOINT_WEIGHT * outputs['keypoint_loss']
                if 'spatial_loss' in outputs:
                    loss += cfg.TRAIN.SPATIAL_WEIGHT * outputs['spatial_loss']
                if 'temporal_loss' in outputs:
                    loss += cfg.TRAIN.TEMPORAL_WEIGHT * outputs['temporal_loss']

            # Backward pass
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
        else:
            outputs = model(images, labels)
            
            # Compute weighted loss
            loss = 0.0
            if 'keypoint_loss' in outputs:
                loss += cfg.TRAIN.KEYPOINT_WEIGHT * outputs['keypoint_loss']
            if 'spatial_loss' in outputs:
                loss += cfg.TRAIN.SPATIAL_WEIGHT * outputs['spatial_loss']
            if 'temporal_loss' in outputs:
                loss += cfg.TRAIN.TEMPORAL_WEIGHT * outputs['temporal_loss']
            
            # Backward pass
            loss.backward()
            optimizer.step()
        
        # Update statistics
        total_loss += loss.item()
        if 'keypoint_loss' in outputs:
            total_keypoint_loss += outputs['keypoint_loss'].item()
        if 'spatial_loss' in outputs:
            total_spatial_loss += outputs['spatial_loss'].item()
        if 'temporal_loss' in outputs:
            total_temporal_loss += outputs['temporal_loss'].item()
        
        # Update global step
        global_step += 1

        # Log progress
        if batch_idx % 100 == 0:
            logger.info(f'Epoch {epoch}, Batch {batch_idx}/{len(train_loader)}, '
                       f'Loss: {loss.item():.4f}')

        # Step-based visualization
        if global_step % cfg.OUTPUT.VIS_STEP_FREQ == 0:
            logger.info(f'Generating visualization at step {global_step}...')
            try:
                # Save current training state
                current_training_mode = model.training
                model.eval()

                # Create visualization directory
                vis_dir = os.path.join(cfg.OUTPUT.DIR, 'step_visualizations')
                os.makedirs(vis_dir, exist_ok=True)

                # Use current batch for visualization (only 1 image)
                vis_images = images[:1]  # Take first image from current batch
                vis_labels = {}
                for key in labels:
                    if isinstance(labels[key], torch.Tensor):
                        vis_labels[key] = labels[key][:1]
                    elif isinstance(labels[key], list):
                        vis_labels[key] = labels[key][:1]

                # Get model predictions
                with torch.no_grad():
                    vis_predictions = model(vis_images)

                # Save visualization
                vis_save_path = os.path.join(vis_dir, f'step_{global_step:06d}.png')
                visualize_predictions(vis_images, vis_predictions, vis_labels,
                                    save_path=vis_save_path, epoch=epoch, step=global_step)
                logger.info(f'Step visualization saved: {vis_save_path}')

                # Restore training state
                model.train(current_training_mode)

            except Exception as e:
                logger.warning(f'Step visualization failed: {e}')
    
    # Average losses
    avg_loss = total_loss / len(train_loader)
    avg_keypoint_loss = total_keypoint_loss / len(train_loader)
    avg_spatial_loss = total_spatial_loss / len(train_loader)
    avg_temporal_loss = total_temporal_loss / len(train_loader)
    
    return {
        'total_loss': avg_loss,
        'keypoint_loss': avg_keypoint_loss,
        'spatial_loss': avg_spatial_loss,
        'temporal_loss': avg_temporal_loss
    }, global_step


def validate_epoch(model, val_loader, cfg):
    """Validate for one epoch"""
    model.eval()

    total_loss = 0.0
    total_keypoint_loss = 0.0
    total_spatial_loss = 0.0
    total_temporal_loss = 0.0
    num_batches = 0

    device = torch.device(cfg.DEVICE)

    with torch.no_grad():
        for batch_idx, (images, labels) in enumerate(val_loader):
            # Move data to device
            images = images.to(device)
            for key in labels:
                if isinstance(labels[key], torch.Tensor):
                    labels[key] = labels[key].to(device)

            # Forward pass - force training mode for loss computation
            model.train()
            outputs = model(images, labels)
            model.eval()

            # Compute loss
            loss = torch.tensor(0.0, device=device)
            if 'keypoint_loss' in outputs:
                loss += cfg.TRAIN.KEYPOINT_WEIGHT * outputs['keypoint_loss']
            if 'spatial_loss' in outputs:
                loss += cfg.TRAIN.SPATIAL_WEIGHT * outputs['spatial_loss']
            if 'temporal_loss' in outputs:
                loss += cfg.TRAIN.TEMPORAL_WEIGHT * outputs['temporal_loss']

            # Update statistics
            total_loss += loss.item()
            if 'keypoint_loss' in outputs:
                total_keypoint_loss += outputs['keypoint_loss'].item()
            if 'spatial_loss' in outputs:
                total_spatial_loss += outputs['spatial_loss'].item()
            if 'temporal_loss' in outputs:
                total_temporal_loss += outputs['temporal_loss'].item()

            num_batches += 1

            # Limit validation batches for speed
            if batch_idx >= 100:  # Only validate on first 100 batches
                break

    # Average losses
    avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
    avg_keypoint_loss = total_keypoint_loss / num_batches if num_batches > 0 else 0.0
    avg_spatial_loss = total_spatial_loss / num_batches if num_batches > 0 else 0.0
    avg_temporal_loss = total_temporal_loss / num_batches if num_batches > 0 else 0.0

    return {
        'total_loss': avg_loss,
        'keypoint_loss': avg_keypoint_loss,
        'spatial_loss': avg_spatial_loss,
        'temporal_loss': avg_temporal_loss
    }


def main():
    """Main training function"""
    # Parse arguments
    args = parse_args()

    # Get configuration
    cfg = get_config()
    cfg = update_config(cfg, args)

    # Create output directory
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = os.path.join(cfg.OUTPUT.DIR, f'posetrack_{timestamp}')
    os.makedirs(output_dir, exist_ok=True)
    cfg.OUTPUT.DIR = output_dir

    # Setup logger
    logger = setup_logger('train', output_dir)
    logger.info(f'Starting training with config: {cfg}')

    # Set random seed
    torch.manual_seed(cfg.SEED)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(cfg.SEED)

    # Create data loaders
    logger.info('Creating data loaders...')
    train_loader, val_loader, train_dataset, val_dataset = create_data_loaders(cfg)

    # Print dataset statistics
    train_stats = train_dataset.get_statistics()
    val_stats = val_dataset.get_statistics()
    logger.info(f'Training dataset: {train_stats}')
    logger.info(f'Validation dataset: {val_stats}')

    # Create model and optimizer
    logger.info('Creating model and optimizer...')
    model, optimizer, scheduler = create_model_and_optimizer(cfg)

    # Mixed precision scaler
    scaler = GradScaler('cuda') if cfg.MIXED_PRECISION and torch.cuda.is_available() else None

    # Resume from checkpoint if specified
    start_epoch = 0
    best_loss = float('inf')

    if args.resume:
        logger.info(f'Resuming from checkpoint: {args.resume}')
        checkpoint = load_checkpoint(args.resume)
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        if scheduler and 'scheduler_state_dict' in checkpoint:
            scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        start_epoch = checkpoint['epoch'] + 1
        best_loss = checkpoint.get('best_loss', float('inf'))
        logger.info(f'Resumed from epoch {start_epoch}')

    # Training loop
    logger.info('Starting training...')
    global_step = 0  # Initialize global step counter

    for epoch in range(start_epoch, cfg.TRAIN.EPOCHS):
        epoch_start_time = time.time()

        # Train for one epoch
        train_metrics, global_step = train_epoch(model, train_loader, optimizer, scaler, cfg, epoch, logger, global_step)

        # Validate
        val_metrics = validate_epoch(model, val_loader, cfg)

        # Update scheduler
        if scheduler:
            if cfg.TRAIN.LR_SCHEDULE == 'plateau':
                scheduler.step(val_metrics['total_loss'])
            else:
                scheduler.step()

        # Log metrics
        epoch_time = time.time() - epoch_start_time
        logger.info(f'Epoch {epoch}/{cfg.TRAIN.EPOCHS} completed in {epoch_time:.2f}s')
        logger.info(f'Train Loss: {train_metrics["total_loss"]:.4f} '
                   f'(KP: {train_metrics["keypoint_loss"]:.4f}, '
                   f'SP: {train_metrics["spatial_loss"]:.4f}, '
                   f'TP: {train_metrics["temporal_loss"]:.4f})')
        logger.info(f'Val Loss: {val_metrics["total_loss"]:.4f} '
                   f'(KP: {val_metrics["keypoint_loss"]:.4f}, '
                   f'SP: {val_metrics["spatial_loss"]:.4f}, '
                   f'TP: {val_metrics["temporal_loss"]:.4f})')

        # Save checkpoint
        is_best = val_metrics['total_loss'] < best_loss
        if is_best:
            best_loss = val_metrics['total_loss']

        if epoch % cfg.OUTPUT.SAVE_FREQ == 0 or is_best:
            checkpoint_path = save_checkpoint({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
                'train_metrics': train_metrics,
                'val_metrics': val_metrics,
                'best_loss': best_loss,
                'config': cfg
            }, is_best, output_dir)

            logger.info(f'Checkpoint saved: {checkpoint_path}')

        # Evaluate and visualize
        if epoch % cfg.OUTPUT.EVAL_FREQ == 0:
            logger.info('Running evaluation...')
            # TODO: Add detailed evaluation metrics (AP, MOTA, etc.)

        # Generate visualizations
        if epoch % cfg.OUTPUT.VIS_FREQ == 0 or epoch == 0:
            logger.info('Generating visualizations...')
            try:
                # Get a batch from validation set for visualization
                val_iter = iter(val_loader)
                vis_images, vis_labels = next(val_iter)
                vis_images = vis_images[:1].to(torch.device(cfg.DEVICE))  # Take first image

                # Move labels to device
                for key in vis_labels:
                    if isinstance(vis_labels[key], torch.Tensor):
                        vis_labels[key] = vis_labels[key][:1].to(torch.device(cfg.DEVICE))
                    elif isinstance(vis_labels[key], list):
                        vis_labels[key] = vis_labels[key][:1]

                # Get model predictions
                model.eval()
                with torch.no_grad():
                    vis_predictions = model(vis_images)
                model.train()

                # Save visualization
                vis_save_path = os.path.join(output_dir, f'visualizations/epoch_{epoch:03d}.png')
                visualize_predictions(vis_images, vis_predictions, vis_labels,
                                    save_path=vis_save_path, epoch=epoch)
                logger.info(f'Visualization saved: {vis_save_path}')

            except Exception as e:
                logger.warning(f'Visualization failed: {e}')

    logger.info('Training completed!')


if __name__ == '__main__':
    main()
