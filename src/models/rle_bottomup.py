"""
RLE-based Bottom-up Multi-Person Pose Estimation and Tracking Model

This model directly regresses:
1. Keypoint coordinates (x, y) + RLE sigma
2. Spatial connection vectors + RLE sigma  
3. Temporal connection vectors + RLE sigma

No explicit heatmaps or Re-ID features are used.
"""

import numpy as np
import torch
import torch.nn as nn
import torch.distributions as distributions
from easydict import EasyDict

from .backbone import ResNetBackbone
from .real_nvp import RealNVP, nets, nett


class Linear(nn.Module):
    """Custom linear layer with optional normalization"""
    
    def __init__(self, in_channel, out_channel, bias=True, norm=True):
        super(Linear, self).__init__()
        self.bias = bias
        self.norm = norm
        self.linear = nn.Linear(in_channel, out_channel, bias)
        nn.init.xavier_uniform_(self.linear.weight, gain=0.01)

    def forward(self, x):
        y = x.matmul(self.linear.weight.t())

        if self.norm:
            x_norm = torch.norm(x, dim=1, keepdim=True)
            y = y / x_norm

        if self.bias:
            y = y + self.linear.bias
        return y


class RLEBottomUpModel(nn.Module):
    """
    RLE-based Bottom-up Multi-Person Pose Estimation and Tracking Model
    
    Outputs:
    - Keypoint coordinates (x, y) for each joint type
    - Spatial connection vectors between joints of the same person
    - Temporal connection vectors between frames for tracking
    - RLE sigma values for uncertainty estimation of all above
    """
    
    def __init__(self, config):
        super(RLEBottomUpModel, self).__init__()
        
        self.config = config
        self.num_joints = config.NUM_JOINTS
        self.image_size = config.IMAGE_SIZE
        self.fc_dim = config.FC_DIM
        self.num_spatial_connections = len(config.SKELETON)  # Number of bone connections
        
        # Backbone network
        self.backbone = ResNetBackbone(
            num_layers=config.BACKBONE_LAYERS,
            pretrained=config.BACKBONE_PRETRAINED
        )
        
        # Keep spatial dimensions for bottom-up multi-person approach
        # Use dense prediction: each spatial location can predict keypoints
        self.feature_size = 8  # 8x8 spatial grid for dense prediction

        # Convolutional heads for dense prediction (instead of FC layers)
        backbone_channels = self.backbone.feature_channels

        # 1. Keypoint detection heads - predict at each spatial location
        self.keypoint_conv = nn.Conv2d(backbone_channels, self.num_joints, 3, padding=1)
        self.keypoint_sigma_conv = nn.Conv2d(backbone_channels, self.num_joints, 3, padding=1)

        # 2. Spatial connection heads - predict connection vectors at each location
        self.spatial_conv = nn.Conv2d(backbone_channels, self.num_spatial_connections * 2, 3, padding=1)
        self.spatial_sigma_conv = nn.Conv2d(backbone_channels, self.num_spatial_connections * 2, 3, padding=1)

        # 3. Temporal connection heads - predict temporal displacement at each location
        self.temporal_conv = nn.Conv2d(backbone_channels, self.num_joints * 2, 3, padding=1)
        self.temporal_sigma_conv = nn.Conv2d(backbone_channels, self.num_joints * 2, 3, padding=1)

        # Initialize convolutional heads
        for conv in [self.keypoint_conv, self.spatial_conv, self.temporal_conv]:
            nn.init.normal_(conv.weight, std=0.01)
            nn.init.constant_(conv.bias, 0)

        for conv in [self.keypoint_sigma_conv, self.spatial_sigma_conv, self.temporal_sigma_conv]:
            nn.init.normal_(conv.weight, std=0.01)
            nn.init.constant_(conv.bias, -2.0)  # Start with low uncertainty
        
        # RLE Flow models for each prediction type
        prior = distributions.MultivariateNormal(torch.zeros(2), torch.eye(2))
        masks = torch.from_numpy(np.array([[0, 1], [1, 0]] * 3).astype(np.float32))
        
        self.flow_keypoint = RealNVP(nets, nett, masks, prior)
        self.flow_spatial = RealNVP(nets, nett, masks, prior)
        self.flow_temporal = RealNVP(nets, nett, masks, prior)
        
        # Store skeleton connections for spatial connection processing
        self.skeleton = config.SKELETON
    
    def forward(self, x, labels=None):
        """
        Forward pass for multi-person pose estimation

        Args:
            x: Input images (B, 3, H, W)
            labels: Ground truth labels for training (optional)

        Returns:
            Dictionary containing dense predictions and losses
        """
        batch_size = x.shape[0]

        # Extract features using backbone
        feat = self.backbone(x)  # (B, C, H/32, W/32)

        # Resize to target feature size for dense prediction
        feat = torch.nn.functional.interpolate(
            feat, size=(self.feature_size, self.feature_size),
            mode='bilinear', align_corners=False
        )  # (B, C, 8, 8)

        # Dense keypoint predictions at each spatial location
        # Each location can detect keypoints for different people
        keypoint_logits = self.keypoint_conv(feat)  # (B, J, 8, 8)
        keypoint_heatmaps = torch.sigmoid(keypoint_logits)  # (B, J, 8, 8)
        keypoint_sigma_maps = torch.sigmoid(self.keypoint_sigma_conv(feat))  # (B, J, 8, 8)

        # Dense spatial connection predictions
        spatial_maps = self.spatial_conv(feat)  # (B, S*2, 8, 8)
        spatial_sigma_maps = torch.sigmoid(self.spatial_sigma_conv(feat))  # (B, S*2, 8, 8)

        # Dense temporal connection predictions
        temporal_maps = self.temporal_conv(feat)  # (B, J*2, 8, 8)
        temporal_sigma_maps = torch.sigmoid(self.temporal_sigma_conv(feat))  # (B, J*2, 8, 8)

        # Reshape spatial and temporal maps
        spatial_maps = spatial_maps.view(batch_size, self.num_spatial_connections, 2,
                                       self.feature_size, self.feature_size)
        spatial_sigma_maps = spatial_sigma_maps.view(batch_size, self.num_spatial_connections, 2,
                                                   self.feature_size, self.feature_size)

        temporal_maps = temporal_maps.view(batch_size, self.num_joints, 2,
                                         self.feature_size, self.feature_size)
        temporal_sigma_maps = temporal_sigma_maps.view(batch_size, self.num_joints, 2,
                                                     self.feature_size, self.feature_size)

        # Compute confidence scores (1 - sigma)
        keypoint_scores = 1 - keypoint_sigma_maps
        spatial_scores = 1 - torch.mean(spatial_sigma_maps, dim=2)  # Average over x,y
        temporal_scores = 1 - torch.mean(temporal_sigma_maps, dim=2)  # Average over x,y
        
        # Compute RLE losses during training
        losses = {}
        if self.training and labels is not None:
            losses = self._compute_dense_rle_losses(
                keypoint_logits, keypoint_heatmaps,
                spatial_maps, temporal_maps,
                labels
            )

        output = EasyDict(
            # Dense keypoint predictions
            keypoint_heatmaps=keypoint_heatmaps,
            keypoint_sigma_maps=keypoint_sigma_maps,
            keypoint_scores=keypoint_scores,

            # Dense spatial connection predictions
            spatial_maps=spatial_maps,
            spatial_sigma_maps=spatial_sigma_maps,
            spatial_scores=spatial_scores,

            # Dense temporal connection predictions
            temporal_maps=temporal_maps,
            temporal_sigma_maps=temporal_sigma_maps,
            temporal_scores=temporal_scores,

            # Losses
            **losses
        )

        return output

    def _compute_dense_rle_losses(self, keypoint_logits, keypoint_heatmaps, spatial_maps, temporal_maps, labels):
        """
        Compute dense RLE losses for multi-person pose estimation

        Args:
            keypoint_heatmaps: Dense keypoint predictions (B, J, H, W)
            keypoint_sigma_maps: Dense keypoint uncertainties (B, J, H, W)
            spatial_maps: Dense spatial connection vectors (B, S, 2, H, W)
            spatial_sigma_maps: Dense spatial uncertainties (B, S, 2, H, W)
            temporal_maps: Dense temporal connection vectors (B, J, 2, H, W)
            temporal_sigma_maps: Dense temporal uncertainties (B, J, 2, H, W)
            labels: Ground truth labels
            batch_size: Batch size

        Returns:
            Dictionary of losses
        """
        losses = {}

        # For now, use simplified losses for dense prediction
        # TODO: Implement proper multi-person GT handling

        # 1. Keypoint detection loss (simplified)
        if 'keypoint_gt' in labels:
            # Create target heatmaps from GT keypoints
            target_heatmaps = self._create_target_heatmaps(labels['keypoint_gt'], labels['visibility'])

            # Binary cross entropy loss with logits (autocast safe)
            keypoint_loss = torch.nn.functional.binary_cross_entropy_with_logits(
                keypoint_logits, target_heatmaps, reduction='mean'
            )
            losses['keypoint_loss'] = keypoint_loss

        # 2. Spatial connection loss (simplified)
        if 'spatial_gt' in labels:
            # Use L2 loss for spatial connections (simplified)
            spatial_loss = torch.nn.functional.mse_loss(
                spatial_maps.mean(dim=(-2, -1)),  # Average over spatial dimensions
                labels['spatial_gt'], reduction='mean'
            )
            losses['spatial_loss'] = spatial_loss

        # 3. Temporal connection loss (simplified)
        if 'temporal_gt' in labels:
            # Use L2 loss for temporal connections (simplified)
            temporal_loss = torch.nn.functional.mse_loss(
                temporal_maps.mean(dim=(-2, -1)),  # Average over spatial dimensions
                labels['temporal_gt'], reduction='mean'
            )
            losses['temporal_loss'] = temporal_loss

        # Total loss
        total_loss = 0
        if 'keypoint_loss' in losses:
            total_loss += losses['keypoint_loss']
        if 'spatial_loss' in losses:
            total_loss += losses['spatial_loss']
        if 'temporal_loss' in losses:
            total_loss += losses['temporal_loss']

        losses['total_loss'] = total_loss

        return losses

    def _create_target_heatmaps(self, gt_keypoints, visibility):
        """
        Create target heatmaps from GT keypoints

        Args:
            gt_keypoints: Ground truth keypoints (B, J, 2) normalized to [0,1]
            visibility: Visibility flags (B, J)

        Returns:
            Target heatmaps (B, J, H, W)
        """
        batch_size = gt_keypoints.shape[0]
        target_heatmaps = torch.zeros(
            batch_size, self.num_joints, self.feature_size, self.feature_size,
            device=gt_keypoints.device
        )

        for b in range(batch_size):
            for j in range(self.num_joints):
                if visibility[b, j] > 0:
                    # Convert normalized coordinates to feature map coordinates
                    x = int(gt_keypoints[b, j, 0] * self.feature_size)
                    y = int(gt_keypoints[b, j, 1] * self.feature_size)

                    # Clamp to valid range
                    x = max(0, min(x, self.feature_size - 1))
                    y = max(0, min(y, self.feature_size - 1))

                    # Set target heatmap
                    target_heatmaps[b, j, y, x] = 1.0

        return target_heatmaps
